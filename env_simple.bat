@echo off
setlocal enabledelayedexpansion

if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="/?" goto help

set "action=%1"

if /i "%action%"=="list" goto list
if /i "%action%"=="get" goto get
if /i "%action%"=="set" goto set
if /i "%action%"=="delete" goto delete

echo Invalid action: %action%
goto help

:help
echo.
echo Windows Environment Variable Manager
echo ====================================
echo.
echo Usage: env_simple.bat ^<action^> [name] [value]
echo.
echo Actions:
echo   list         - List all user environment variables
echo   get ^<name^>   - Get environment variable value
echo   set ^<name^> ^<value^> - Set environment variable
echo   delete ^<name^> - Delete environment variable
echo   help         - Show this help
echo.
echo Examples:
echo   env_simple.bat list
echo   env_simple.bat get PATH
echo   env_simple.bat set MY_VAR "Hello World"
echo   env_simple.bat delete MY_VAR
echo.
goto end

:list
echo.
echo User Environment Variables:
echo ===========================
reg query "HKEY_CURRENT_USER\Environment" 2>nul
if errorlevel 1 echo Cannot access registry.
goto end

:get
if "%2"=="" (
    echo Error: Please provide variable name.
    goto end
)
echo.
echo Variable: %2
reg query "HKEY_CURRENT_USER\Environment" /v "%2" 2>nul
if errorlevel 1 echo Variable '%2' not found.
goto end

:set
if "%2"=="" (
    echo Error: Please provide variable name.
    goto end
)
if "%3"=="" (
    echo Error: Please provide variable value.
    goto end
)
echo.
echo Setting: %2 = %3
reg add "HKEY_CURRENT_USER\Environment" /v "%2" /t REG_SZ /d "%3" /f >nul
if errorlevel 1 (
    echo Error: Cannot set variable.
) else (
    echo Success: Variable '%2' set.
    echo Note: Restart command prompt for changes to take effect.
)
goto end

:delete
if "%2"=="" (
    echo Error: Please provide variable name.
    goto end
)
echo.
echo Deleting: %2
reg delete "HKEY_CURRENT_USER\Environment" /v "%2" /f >nul 2>&1
if errorlevel 1 (
    echo Error: Variable '%2' not found or cannot delete.
) else (
    echo Success: Variable '%2' deleted.
    echo Note: Restart command prompt for changes to take effect.
)
goto end

:end
endlocal
