package com.seres.background.upgrade.model

import com.google.gson.annotations.SerializedName

/**
 * 升级任务信息根对象
 */
data class UpgradeTaskInfo(
    val edition: Edition = Edition(),
    val dpPackage: DpPackage = DpPackage()
)

/**
 * 版本信息
 */
data class Edition(
    val prettyVersion: String = ""
)

/**
 * DP包信息
 */
data class DpPackage(
    val vin: String = "",
    @SerializedName("pkg_id")
    val pkgId: String = "",
    @SerializedName("pkg_info")
    val pkgInfo: PkgInfo = PkgInfo()
)

/**
 * 包信息
 */
data class PkgInfo(
    @SerializedName("pkg_ver")
    val pkgVer: String = "",
    @SerializedName("dc_list")
    val dcList: List<DcItem> = emptyList(),
    @SerializedName("ecu_list")
    val ecuList: List<EcuItem> = emptyList(),
    val rules: Rules = Rules(),
    @SerializedName("upgrade_order")
    val upgradeOrder: UpgradeOrder = UpgradeOrder()
)

/**
 * DC项（升级包文件信息）
 */
data class DcItem(
    val domain: Int = 0,
    val path: String = "",
    @SerializedName("zip_size_in_byte")
    val zipSizeInByte: Long = 0,
    @SerializedName("node_addr")
    val nodeAddr: String = "",
    @SerializedName("soft_ver")
    val softVer: String = "",
    @SerializedName("pkg_type")
    val pkgType: Int = 0,
    val sha256: String = ""
)

/**
 * ECU项
 */
data class EcuItem(
    val domain: Int = 0,
    @SerializedName("ecu_id")
    val ecuId: String = "",
    @SerializedName("node_addr")
    val nodeAddr: String = "",
    val seamless: Int = 0
)

/**
 * 规则
 */
data class Rules(
    val version: Int = 1,
    val expr: Expr = Expr()
)

/**
 * 表达式
 */
data class Expr(
    @SerializedName("pre_install")
    val preInstall: PreInstall = PreInstall()
)

/**
 * 预安装条件
 */
data class PreInstall(
    val elms: List<Element> = emptyList()
)

/**
 * 条件元素
 */
data class Element(
    val `var`: String = "",
    val `val`: String = ""
)

/**
 * 升级顺序
 */
data class UpgradeOrder(
    val install: OrderStage = OrderStage(),
    val rollback: OrderStage = OrderStage(),
    val active: OrderStage = OrderStage()
)

/**
 * 顺序阶段
 */
data class OrderStage(
    val stages: List<Stage> = emptyList()
)

/**
 * 阶段
 */
data class Stage(
    @SerializedName("voltage_type")
    val voltageType: Int = 1,
    @SerializedName("order_node_addr")
    val orderNodeAddr: List<List<String>> = emptyList()
)

/**
 * 前置条件数据类
 */
data class Preconditions(
    val powerBatteryThreshold: Int = 15,
    val batteryThreshold: Int = 65
)

/**
 * 升级包信息数据类
 */
data class PackageInfo(
    val ecuName: String = "",
    val seamless: Boolean = true,
    val packagePath: String = "",
    val packageSize: Long = 0L,
    val packageMd5: String = ""
)

/**
 * 升级任务数据类
 */
data class UpgradeTask(
    val version: String = "1.0",
    val sharePath: String = "/ota_share/",
    val packageInfo: List<PackageInfo> = emptyList(),
    val preconditions: Preconditions = Preconditions(),
    val preferences: Map<String, Any> = emptyMap()
)

/**
 * 升级任务信息（管理器使用）
 */
data class UpgradeTaskManagerInfo(
    val taskId: String,
    val upgradeTask: UpgradeTask? = null,
    val upgradeTaskInfo: UpgradeTaskInfo? = null,
    val usbPath: String,
    val status: TaskStatus,
    val validPackages: List<DcItem> = emptyList(),
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis()
)

/**
 * 任务状态枚举
 */
enum class TaskStatus {
    PENDING,    // 等待中
    COPYING,    // 拷贝文件中
    READY,      // 准备就绪
    PUBLISHED,  // 已发布
    RUNNING,    // 运行中
    COMPLETED,  // 已完成
    FAILED,     // 失败
    CANCELLED   // 已取消
}

/**
 * 资产信息数据类
 */
data class InventoryInfo(
    val partNumber: String = "",
    val softwareVersion: String = "",
    val supplierCode: String = "",
    val ecuName: String = "",
    val serialNumber: String = "",
    val hardwareVersion: String = "",
    val bootloaderVersion: String = "",
    val backupVersion: String = ""
)
