@echo off
setlocal enabledelayedexpansion

if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="/?" goto help

set "action=%1"

if /i "%action%"=="get" goto get_path
if /i "%action%"=="add" goto add_path
if /i "%action%"=="set" goto set_path
if /i "%action%"=="remove" goto remove_path

echo Invalid action: %action%
goto help

:help
echo.
echo PATH Environment Variable Manager
echo =================================
echo.
echo Usage: set_path.bat ^<action^> [path]
echo.
echo Actions:
echo   get              - Show current PATH
echo   add ^<path^>       - Add path to beginning of PATH
echo   set ^<full_path^>  - Set complete PATH (use with caution)
echo   remove ^<path^>    - Remove specific path from PATH
echo   help             - Show this help
echo.
echo Examples:
echo   set_path.bat get
echo   set_path.bat add "C:\Program Files\YunShu\utils"
echo   set_path.bat remove "C:\Program Files\YunShu\utils"
echo.
goto end

:get_path
echo.
echo Current PATH:
echo =============
reg query "HKEY_CURRENT_USER\Environment" /v "PATH" 2>nul
if errorlevel 1 (
    echo No user PATH found, showing system PATH:
    echo %PATH%
)
goto end

:add_path
if "%2"=="" (
    echo Error: Please provide path to add.
    goto end
)

REM Get current user PATH
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Environment" /v "PATH" 2^>nul') do set "current_path=%%b"

REM Remove quotes from new path
set "new_path=%~2"

REM Check if path already exists
echo !current_path! | findstr /i /c:"!new_path!" >nul
if not errorlevel 1 (
    echo Path already exists in PATH: !new_path!
    goto end
)

REM Add new path to beginning
if defined current_path (
    set "updated_path=!new_path!;!current_path!"
) else (
    set "updated_path=!new_path!"
)

echo.
echo Adding to PATH: !new_path!
reg add "HKEY_CURRENT_USER\Environment" /v "PATH" /t REG_EXPAND_SZ /d "!updated_path!" /f >nul
if errorlevel 1 (
    echo Error: Cannot update PATH.
) else (
    echo Success: PATH updated.
    echo Note: Restart command prompt for changes to take effect.
)
goto end

:set_path
if "%2"=="" (
    echo Error: Please provide complete PATH value.
    goto end
)

REM Collect all arguments as the path value
set "new_path="
shift
:collect_path_args
if "%1"=="" goto do_set_path
if defined new_path (
    set "new_path=!new_path! %1"
) else (
    set "new_path=%1"
)
shift
goto collect_path_args

:do_set_path
REM Remove surrounding quotes if present
if "!new_path:~0,1!"=="""" if "!new_path:~-1!"=="""" (
    set "new_path=!new_path:~1,-1!"
)

echo.
echo Setting complete PATH to:
echo !new_path!
echo.
echo WARNING: This will replace your entire user PATH!
set /p "confirm=Are you sure? (y/N): "
if /i not "!confirm!"=="y" (
    echo Operation cancelled.
    goto end
)

reg add "HKEY_CURRENT_USER\Environment" /v "PATH" /t REG_EXPAND_SZ /d "!new_path!" /f >nul
if errorlevel 1 (
    echo Error: Cannot set PATH.
) else (
    echo Success: PATH set.
    echo Note: Restart command prompt for changes to take effect.
)
goto end

:remove_path
if "%2"=="" (
    echo Error: Please provide path to remove.
    goto end
)

REM Get current user PATH
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Environment" /v "PATH" 2^>nul') do set "current_path=%%b"

if not defined current_path (
    echo No user PATH found.
    goto end
)

set "path_to_remove=%~2"
set "updated_path="

REM Split PATH and rebuild without the specified path
for %%i in ("!current_path:;=" "!") do (
    set "current_item=%%~i"
    if /i not "!current_item!"=="!path_to_remove!" (
        if defined updated_path (
            set "updated_path=!updated_path!;!current_item!"
        ) else (
            set "updated_path=!current_item!"
        )
    )
)

echo.
echo Removing from PATH: !path_to_remove!
reg add "HKEY_CURRENT_USER\Environment" /v "PATH" /t REG_EXPAND_SZ /d "!updated_path!" /f >nul
if errorlevel 1 (
    echo Error: Cannot update PATH.
) else (
    echo Success: PATH updated.
    echo Note: Restart command prompt for changes to take effect.
)
goto end

:end
endlocal
