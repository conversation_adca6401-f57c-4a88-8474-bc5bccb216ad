#!/bin/bash

# 后台升级服务快速验证脚本
# 使用方法: ./check_service.sh

echo "=== 后台升级服务快速验证 ==="
echo "时间: $(date)"
echo ""

# 检查ADB连接
if ! adb devices | grep -q "device$"; then
    echo "❌ 错误: 未检测到ADB设备连接"
    echo "请确保设备已连接并启用USB调试"
    exit 1
fi

echo "✅ ADB设备连接正常"
echo ""

# 1. 检查应用安装
echo "1. 检查应用安装状态:"
if adb shell pm list packages | grep -q com.seres.background.upgrade; then
    echo "   ✅ 应用已安装"
    
    # 获取应用版本信息
    version_info=$(adb shell dumpsys package com.seres.background.upgrade | grep versionName)
    if [ -n "$version_info" ]; then
        echo "   📋 $version_info"
    fi
else
    echo "   ❌ 应用未安装"
    echo "   请先安装应用: adb install UDiskBackgroundUpgradeService-debug.apk"
    exit 1
fi
echo ""

# 2. 检查服务进程
echo "2. 检查服务进程:"
process_info=$(adb shell ps | grep com.seres.background.upgrade)
if [ -n "$process_info" ]; then
    echo "   ✅ 服务进程正在运行"
    echo "   📋 进程信息: $process_info"
else
    echo "   ❌ 服务进程未运行"
    echo "   尝试启动服务..."
    adb shell am start-foreground-service com.seres.background.upgrade/.service.UDiskBackgroundUpgradeService
    sleep 2
    if adb shell ps | grep -q com.seres.background.upgrade; then
        echo "   ✅ 服务启动成功"
    else
        echo "   ❌ 服务启动失败"
    fi
fi
echo ""

# 3. 检查状态文件目录
echo "3. 检查状态文件:"
status_dir="/Android/data/com.seres.background.upgrade/files/status"
if adb shell ls "$status_dir" >/dev/null 2>&1; then
    echo "   ✅ 状态目录存在: $status_dir"
    
    # 检查各个状态文件
    files=("heartbeat.txt" "service_status.json" "usb_status.json" "task_status.json")
    for file in "${files[@]}"; do
        if adb shell test -f "$status_dir/$file"; then
            echo "   📄 $file: 存在"
            if [ "$file" = "heartbeat.txt" ]; then
                heartbeat=$(adb shell cat "$status_dir/$file" 2>/dev/null)
                if [ -n "$heartbeat" ]; then
                    echo "      💓 $heartbeat"
                fi
            fi
        else
            echo "   📄 $file: 不存在"
        fi
    done
else
    echo "   ❌ 状态目录不存在"
    echo "   这可能表示服务未正常启动或权限问题"
fi
echo ""

# 4. 检查日志文件
echo "4. 检查日志文件:"
log_dir="/Android/data/com.seres.background.upgrade/files/logs"
if adb shell ls "$log_dir" >/dev/null 2>&1; then
    echo "   ✅ 日志目录存在: $log_dir"
    log_files=$(adb shell ls "$log_dir" 2>/dev/null)
    if [ -n "$log_files" ]; then
        echo "   📄 日志文件:"
        echo "$log_files" | sed 's/^/      /'
    else
        echo "   📄 日志目录为空"
    fi
else
    echo "   ❌ 日志目录不存在"
fi
echo ""

# 5. 检查最近日志
echo "5. 检查最近应用日志:"
recent_logs=$(adb logcat -d | grep "BackgroundUpgrade-" | tail -5)
if [ -n "$recent_logs" ]; then
    echo "   ✅ 发现最近日志 (最后5条):"
    echo "$recent_logs" | sed 's/^/      /'
else
    echo "   ⚠️  未发现最近日志"
    echo "   这可能表示服务刚启动或日志级别设置问题"
fi
echo ""

# 6. 检查通知状态
echo "6. 检查前台服务通知:"
notification_info=$(adb shell dumpsys notification | grep "com.seres.background.upgrade" | head -3)
if [ -n "$notification_info" ]; then
    echo "   ✅ 发现前台服务通知"
    echo "$notification_info" | sed 's/^/      /'
else
    echo "   ⚠️  未发现前台服务通知"
fi
echo ""

# 7. 权限检查
echo "7. 检查关键权限:"
permissions=("android.permission.RECEIVE_BOOT_COMPLETED" "android.permission.FOREGROUND_SERVICE" "android.permission.MANAGE_EXTERNAL_STORAGE")
for perm in "${permissions[@]}"; do
    if adb shell dumpsys package com.seres.background.upgrade | grep -q "$perm.*granted=true"; then
        echo "   ✅ $perm: 已授权"
    else
        echo "   ❌ $perm: 未授权"
    fi
done
echo ""

# 8. 提供调试建议
echo "8. 调试建议:"
echo "   📋 查看实时日志: adb logcat | grep 'BackgroundUpgrade-'"
echo "   📋 启动调试界面: adb shell am start -n com.seres.background.upgrade/.DebugActivity"
echo "   📋 查看详细状态: adb shell cat $status_dir/service_status.json"
echo "   📋 模拟USB插入: 参考 debug_commands.md 中的测试命令"
echo ""

# 9. 总结
echo "=== 验证总结 ==="
if adb shell ps | grep -q com.seres.background.upgrade && adb shell test -f "$status_dir/heartbeat.txt"; then
    echo "🎉 后台升级服务运行正常!"
    echo "   服务进程正在运行且心跳正常"
else
    echo "⚠️  后台升级服务可能存在问题"
    echo "   请检查上述输出信息并参考故障排除指南"
fi

echo ""
echo "验证完成! $(date)"
