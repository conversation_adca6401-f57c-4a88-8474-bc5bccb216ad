package com.seres.background.upgrade.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.seres.background.upgrade.manager.UpgradeTaskManager
import com.seres.background.upgrade.model.InventoryInfo
import com.seres.background.upgrade.monitor.StatusMonitor
import com.seres.background.upgrade.publisher.S2STaskPublisher
import com.seres.background.upgrade.utils.LogUtils
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 后台升级服务
 * 主要的后台服务，负责协调各个组件的工作
 */
class UDiskBackgroundUpgradeService : Service(), S2STaskPublisher.VersionCompatibilityChecker {
    
    private val TAG = "UDiskBackgroundUpgradeService"
    
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var s2sTaskPublisher: S2STaskPublisher? = null
    private var usbDetectionService: Intent? = null
    private var statusMonitor: StatusMonitor? = null
    private var heartbeatExecutor: ScheduledExecutorService? = null
    
    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "background_upgrade_channel"
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UDiskBackgroundUpgradeService onCreate")
        
        // 创建前台服务通知
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 初始化组件
        initializeComponents()

        // 启动USB检测服务
        startUsbDetectionService()

        // 启动心跳监控
        startHeartbeatMonitor()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "UDiskBackgroundUpgradeService onStartCommand - flags: $flags, startId: $startId")

        // 确保前台服务正常运行
        try {
            startForeground(NOTIFICATION_ID, createNotification())
            LogUtils.i(TAG, "前台服务通知已更新")
        } catch (e: Exception) {
            LogUtils.e(TAG, "更新前台服务通知失败: ${e.message}")
        }

        // 重新初始化组件（防止重启后组件丢失）
        if (upgradeTaskManager == null || s2sTaskPublisher == null) {
            LogUtils.i(TAG, "检测到组件丢失，重新初始化")
            initializeComponents()
        }

        // 重新启动USB检测服务
        if (usbDetectionService == null) {
            LogUtils.i(TAG, "重新启动USB检测服务")
            startUsbDetectionService()
        }

        // 重新启动心跳监控
        if (heartbeatExecutor?.isShutdown != false) {
            LogUtils.i(TAG, "重新启动心跳监控")
            startHeartbeatMonitor()
        }

        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UDiskBackgroundUpgradeService onDestroy - 尝试重启服务")

        // 在服务被销毁时尝试重启
        try {
            val restartIntent = Intent(this, UDiskBackgroundUpgradeService::class.java)
            restartIntent.action = "com.seres.background.upgrade.RESTART_SERVICE"

            // 延迟重启，避免立即重启被系统阻止
            val pendingIntent = android.app.PendingIntent.getService(
                this,
                1001,
                restartIntent,
                android.app.PendingIntent.FLAG_ONE_SHOT or android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = getSystemService(android.content.Context.ALARM_SERVICE) as android.app.AlarmManager
            alarmManager.setExact(
                android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                android.os.SystemClock.elapsedRealtime() + 5000, // 5秒后重启
                pendingIntent
            )

            LogUtils.i(TAG, "已设置服务重启定时器")
        } catch (e: Exception) {
            LogUtils.e(TAG, "设置服务重启失败: ${e.message}")
        }

        // 清理资源
        cleanup()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        LogUtils.i(TAG, "UDiskBackgroundUpgradeService onTaskRemoved - 任务被移除")

        // 任务被移除时也尝试重启
        try {
            val restartIntent = Intent(this, UDiskBackgroundUpgradeService::class.java)
            restartIntent.action = "com.seres.background.upgrade.RESTART_SERVICE"
            startService(restartIntent)
            LogUtils.i(TAG, "任务移除后重启服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "任务移除后重启服务失败: ${e.message}")
        }
    }
    
    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        try {
            // 初始化状态监控器
            statusMonitor = StatusMonitor(this)

            // 记录服务启动状态
            statusMonitor?.recordServiceStatus("UDiskBackgroundUpgradeService", true)

            // 初始化升级任务管理器
            upgradeTaskManager = UpgradeTaskManager.getInstance(this)

            // 初始化S2S任务发布者
            s2sTaskPublisher = S2STaskPublisher(this).apply {
                setVersionCompatibilityChecker(this@UDiskBackgroundUpgradeService)
            }

            // 设置升级任务管理器的S2S发布者
            upgradeTaskManager?.setS2STaskPublisher(s2sTaskPublisher!!)

            LogUtils.i(TAG, "组件初始化完成")

            // 输出状态文件路径信息
            statusMonitor?.getStatusFilePaths()?.forEach { (key, path) ->
                LogUtils.i(TAG, "状态文件 $key: $path")
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "组件初始化失败: ${e.message}")
        }
    }
    
    /**
     * 启动USB检测服务
     */
    private fun startUsbDetectionService() {
        try {
            usbDetectionService = Intent(this, UsbDetectionService::class.java)
            startForegroundService(usbDetectionService!!)
            LogUtils.i(TAG, "USB检测服务启动成功")

            // 记录USB检测服务状态
            statusMonitor?.recordServiceStatus("UsbDetectionService", true)

        } catch (e: Exception) {
            LogUtils.e(TAG, "启动USB检测服务失败: ${e.message}")
        }
    }

    /**
     * 启动心跳监控
     */
    private fun startHeartbeatMonitor() {
        try {
            heartbeatExecutor = Executors.newSingleThreadScheduledExecutor()
            heartbeatExecutor?.scheduleAtFixedRate({
                statusMonitor?.updateHeartbeat()
            }, 0, 30, TimeUnit.SECONDS) // 每30秒更新一次心跳

            LogUtils.i(TAG, "心跳监控启动成功")
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动心跳监控失败: ${e.message}")
        }
    }
    
    /**
     * 版本兼容性检查实现
     */
    override fun checkCompatibility(inventoryInfoList: List<InventoryInfo>, taskId: String): Boolean {
        LogUtils.i(TAG, "开始版本兼容性检查: $taskId")
        
        try {
            // 获取对应的升级任务
            val taskInfo = upgradeTaskManager?.getTask(taskId)
            if (taskInfo == null) {
                LogUtils.w(TAG, "未找到对应的升级任务: $taskId")
                return false
            }
            
            val upgradeTask = taskInfo.upgradeTask
            if (upgradeTask == null) {
                LogUtils.w(TAG, "升级任务信息为空: $taskId")
                return false
            }

            var allCompatible = true

            // 检查每个ECU的版本兼容性
            for (packageInfo in upgradeTask.packageInfo) {
                val ecuName = packageInfo.ecuName
                val inventoryInfo = inventoryInfoList.find { it.ecuName == ecuName }
                
                if (inventoryInfo != null) {
                    val isCompatible = checkEcuVersionCompatibility(
                        ecuName,
                        inventoryInfo.softwareVersion,
                        packageInfo.packagePath
                    )
                    
                    if (!isCompatible) {
                        allCompatible = false
                        LogUtils.w(TAG, "ECU版本不兼容: $ecuName - 当前版本: ${inventoryInfo.softwareVersion}")
                    } else {
                        LogUtils.d(TAG, "ECU版本兼容: $ecuName - 当前版本: ${inventoryInfo.softwareVersion}")
                    }
                } else {
                    LogUtils.w(TAG, "未找到ECU的资产信息: $ecuName")
                    allCompatible = false
                }
            }
            
            LogUtils.i(TAG, "版本兼容性检查结果: $taskId -> $allCompatible")
            return allCompatible
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "版本兼容性检查失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 检查单个ECU的版本兼容性
     */
    private fun checkEcuVersionCompatibility(
        ecuName: String,
        currentVersion: String,
        packagePath: String
    ): Boolean {
        try {
            // 从包路径中提取目标版本信息
            val targetVersion = extractVersionFromPackagePath(packagePath)
            
            if (targetVersion.isEmpty()) {
                LogUtils.w(TAG, "无法从包路径提取版本信息: $packagePath")
                return false
            }
            
            // 比较版本号
            val versionComparison = compareVersions(currentVersion, targetVersion)
            
            // 只有当目标版本高于当前版本时才认为兼容
            val isCompatible = versionComparison < 0
            
            LogUtils.d(TAG, "ECU $ecuName 版本比较: $currentVersion -> $targetVersion, 兼容: $isCompatible")
            
            return isCompatible
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查ECU版本兼容性失败: $ecuName - ${e.message}")
            return false
        }
    }
    
    /**
     * 从包路径中提取版本信息
     */
    private fun extractVersionFromPackagePath(packagePath: String): String {
        try {
            // 从文件名中提取版本号，假设格式为 xxx_v1.2.3.zip
            val fileName = packagePath.substringAfterLast("/")
            val versionRegex = Regex("""v?(\d+\.\d+(?:\.\d+)?)""")
            val match = versionRegex.find(fileName)
            return match?.groupValues?.get(1) ?: ""
        } catch (e: Exception) {
            LogUtils.e(TAG, "提取版本信息失败: $packagePath - ${e.message}")
            return ""
        }
    }
    
    /**
     * 比较版本号
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    private fun compareVersions(version1: String, version2: String): Int {
        try {
            val v1Parts = version1.split(".").map { it.toIntOrNull() ?: 0 }
            val v2Parts = version2.split(".").map { it.toIntOrNull() ?: 0 }
            
            val maxLength = maxOf(v1Parts.size, v2Parts.size)
            
            for (i in 0 until maxLength) {
                val v1Part = v1Parts.getOrNull(i) ?: 0
                val v2Part = v2Parts.getOrNull(i) ?: 0
                
                when {
                    v1Part < v2Part -> return -1
                    v1Part > v2Part -> return 1
                }
            }
            
            return 0
        } catch (e: Exception) {
            LogUtils.e(TAG, "版本号比较失败: $version1 vs $version2 - ${e.message}")
            return 0
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "后台升级服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "后台升级服务运行状态"
            setShowBadge(false)
        }
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("后台升级服务")
            .setContentText("正在监听USB设备和升级任务")
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            // 停止心跳监控
            heartbeatExecutor?.shutdown()

            // 记录服务停止状态
            statusMonitor?.recordServiceStatus("UDiskBackgroundUpgradeService", false)

            // 清理S2S发布者
            s2sTaskPublisher?.cleanup()

            LogUtils.i(TAG, "资源清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理资源时出错: ${e.message}")
        }
    }
}
