@echo off
chcp 65001 >nul
echo === Test Task JSON Generation ===
echo.

echo 1. Clean up previous test...
adb shell rm -rf /sdcard/test_usb_json/
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json
echo    Previous test data cleaned

echo 2. Create test directory and files...
adb shell mkdir -p /sdcard/test_usb_json/

echo 3. Create device_list.json...
adb shell "cat > /sdcard/test_usb_json/device_list.json << 'EOF'
{
    \"device_list\": {
        \"cdc_domain\": [
            {
                \"ecu_name\": \"device1\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1234\"
            },
            {
                \"ecu_name\": \"device2\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1235\"
            }
        ],
        \"mdc_domain\": [
            {
                \"ecu_name\": \"device3\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1236\"
            }
        ],
        \"vdc_domain\": [
            {
                \"ecu_name\": \"device4\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1237\"
            }
        ]
    }
}
EOF"
echo    device_list.json created

echo 4. Create upgrade packages...
adb shell "echo 'Device1 upgrade package' > /sdcard/test_usb_json/device1.zip"
adb shell "echo 'Device2 upgrade package' > /sdcard/test_usb_json/device2_firmware.bin"
adb shell "echo 'Device3 upgrade package' > /sdcard/test_usb_json/device3.hex"
adb shell "echo 'Device4 upgrade package' > /sdcard/test_usb_json/device4_update.img"
echo    Upgrade packages created

echo 5. Verify test files...
adb shell ls -la /sdcard/test_usb_json/
echo.

echo 6. Clear previous logs...
adb logcat -c
echo    Logs cleared

echo 7. Trigger USB mount event...
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_usb_json
echo    USB mount event triggered

echo 8. Wait 10 seconds for processing...
timeout /t 10 >nul

echo 9. Check logs for task processing...
echo    Recent logs:
adb logcat -d | findstr "UpgradeTaskManager\|DeviceListAnalyzer.*升级任务\|DeviceListAnalyzer.*处理"
echo.

echo 10. Check if task JSON was generated...
echo    Latest upgrade task JSON:
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json 2>nul
if %errorlevel% equ 0 (
    echo    [SUCCESS] Task JSON generated successfully!
) else (
    echo    [FAILED] Task JSON not found
)
echo.

echo 11. Check all task JSON files...
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json 2>nul
echo.

echo 12. Check task status...
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/task_status.json 2>nul
echo.

echo 13. Check /ota_share/ directory...
adb shell ls -la /ota_share/ 2>nul
echo.

echo 14. Show debug activity...
adb shell am start -n com.seres.background.upgrade/.DebugActivity
echo    Debug activity started (click to close)

echo.
echo Test completed!
echo If task JSON was generated, the fix is working correctly.
pause
