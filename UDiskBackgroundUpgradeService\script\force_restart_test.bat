@echo off
chcp 65001 >nul
echo === Force Restart Test ===
echo This test will forcefully stop the app and check auto-restart
echo.

echo 1. Check current service status...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [OK] Service is currently running
) else (
    echo    [ERROR] Service is not running, please start it first
    pause
    exit /b 1
)
echo.

echo 2. Force stop the entire application...
adb shell am force-stop com.seres.background.upgrade
echo    Application force stopped
echo.

echo 3. Wait 5 seconds...
timeout /t 5 >nul

echo 4. Check if any process remains...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [INFO] Some processes still running (this is good for auto-restart)
) else (
    echo    [INFO] All processes stopped
)
echo.

echo 5. Trigger auto-restart via boot broadcast...
adb shell am broadcast -a android.intent.action.BOOT_COMPLETED
echo    Boot broadcast sent
echo.

echo 6. Wait 10 seconds for restart...
timeout /t 10 >nul

echo 7. Check if service restarted...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [SUCCESS] Service restarted automatically!
    echo    Auto-restart mechanism is working
) else (
    echo    [FAILED] Service did not restart
    echo    Trying manual restart...
    adb shell am start-foreground-service com.seres.background.upgrade/.service.UDiskBackgroundUpgradeService
    timeout /t 3 >nul
    adb shell ps | findstr background.upgrade
    if %errorlevel% equ 0 (
        echo    [OK] Manual restart successful
    ) else (
        echo    [ERROR] Manual restart also failed
    )
)
echo.

echo 8. Check all services status...
echo    Main service:
adb shell dumpsys activity services | findstr UDiskBackgroundUpgradeService
echo    USB service:
adb shell dumpsys activity services | findstr UsbDetectionService
echo    Watchdog service:
adb shell dumpsys activity services | findstr WatchdogService
echo.

echo 9. Check notifications (should show foreground services)...
adb shell dumpsys notification | findstr background.upgrade
echo.

echo Test completed!
echo If services restarted automatically, the persistence mechanism is working.
pause
