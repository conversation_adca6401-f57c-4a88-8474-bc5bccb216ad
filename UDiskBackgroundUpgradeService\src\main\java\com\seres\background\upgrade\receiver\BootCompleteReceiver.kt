package com.seres.background.upgrade.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.seres.background.upgrade.service.UDiskBackgroundUpgradeService
import com.seres.background.upgrade.utils.LogUtils

/**
 * 开机启动接收器
 * 系统启动后自动启动后台升级服务
 */
class BootCompleteReceiver : BroadcastReceiver() {
    
    private val TAG = "BootCompleteReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_LOCKED_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON" -> {
                LogUtils.i(TAG, "系统启动完成，启动后台升级服务")
                
                try {
                    // 启动后台升级服务
                    val serviceIntent = Intent(context, UDiskBackgroundUpgradeService::class.java)
                    context.startForegroundService(serviceIntent)
                    
                    LogUtils.i(TAG, "后台升级服务启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动后台升级服务失败: ${e.message}")
                }
            }
        }
    }
}
