package seres.s2s.internal;

import seres.s2s.internal.IAsyncResultCallback;
import seres.s2s.internal.IS2SReportListener;

interface IS2SService {
    // 向S2S服务添加
    void registerS2SSignalListener(in int appId, in IS2SReportListener listener, in int[] initSignalHashIdList);

    // 反注册监听S2S服务上传的Listener
    void unregisterS2SSignalListener(int appId);

    // 添加要监听的信号列表
    void subS2SSignalListen(int appId, in int[] signalHashIdList);

    // 移除要监听的信号列表
    void unsubS2SSignalListen(int appId, in int[] signalHashIdList);

    // 通用调用方法
    Bundle invoke(in int appId, in int serviceHashId, in Bundle params);

    // 通用异步调用方法
    oneway void invokeAsync(in int appId, in int serviceHashId, in Bundle params, in IAsyncResultCallback callback);
}